{"name": "patients-h5-100", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix"]}, "dependencies": {"pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "~10.3.0", "husky": "^8.0.0", "jiti": "^2.4.2", "lint-staged": "^16.1.4", "npm-run-all2": "^8.0.4", "postcss-px-to-viewport": "^1.1.1", "prettier": "3.6.2", "sass": "^1.90.0", "typescript": "~5.8.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}